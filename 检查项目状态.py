#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目状态检查工具
检查PyWxDump项目的安装状态和配置
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("1. 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("   ✓ Python版本符合要求 (3.8+)")
        return True
    else:
        print("   ✗ Python版本过低，需要3.8或更高版本")
        return False

def check_pywxdump_installation():
    """检查PyWxDump安装状态"""
    print("\n2. 检查PyWxDump安装状态...")
    
    try:
        import pywxdump
        print(f"   ✓ PyWxDump已安装，版本: {pywxdump.__version__}")
        return True
    except ImportError:
        print("   ✗ PyWxDump未安装")
        return False

def check_wxdump_command():
    """检查wxdump命令是否可用"""
    print("\n3. 检查wxdump命令...")
    
    try:
        result = subprocess.run(['wxdump', '-V'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print(f"   ✓ wxdump命令可用: {version_info}")
            return True
        else:
            print("   ✗ wxdump命令执行失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
        print(f"   ✗ wxdump命令不可用: {e}")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("\n4. 检查目录结构...")
    
    required_dirs = [
        "PyWxDump",
        "data",
        "data/csv",
        "data/processed"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"   ✓ {dir_path} 目录存在")
        else:
            print(f"   ✗ {dir_path} 目录不存在")
            all_exist = False
    
    return all_exist

def check_required_files():
    """检查必要文件"""
    print("\n5. 检查必要文件...")
    
    required_files = [
        "启动PyWxDump.bat",
        "csv_processor.py",
        "PyWxDump使用指南.md",
        "README.md"
    ]
    
    all_exist = True
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"   ✓ {file_path} 文件存在")
        else:
            print(f"   ✗ {file_path} 文件不存在")
            all_exist = False
    
    return all_exist

def check_csv_files():
    """检查CSV文件"""
    print("\n6. 检查CSV文件...")
    
    csv_dir = Path("data/csv")
    if not csv_dir.exists():
        print("   ✗ CSV目录不存在")
        return False
    
    csv_files = list(csv_dir.glob("*.csv"))
    if csv_files:
        print(f"   ✓ 找到 {len(csv_files)} 个CSV文件:")
        for csv_file in csv_files:
            print(f"     - {csv_file.name}")
        return True
    else:
        print("   ⚠ 暂无CSV文件（这是正常的，需要先使用PyWxDump导出）")
        return True

def check_dependencies():
    """检查Python依赖"""
    print("\n7. 检查Python依赖...")
    
    required_packages = [
        'pycryptodomex',
        'pywin32',
        'requests',
        'fastapi',
        'uvicorn'
    ]
    
    all_installed = True
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"   ✓ {package} 已安装")
        except ImportError:
            print(f"   ✗ {package} 未安装")
            all_installed = False
    
    return all_installed

def provide_recommendations():
    """提供建议"""
    print("\n" + "="*50)
    print("建议和下一步操作:")
    print("="*50)
    
    print("\n如果检查发现问题，请按以下步骤解决：")
    print("\n1. 如果PyWxDump未安装:")
    print("   cd PyWxDump")
    print("   pip install -e .")
    
    print("\n2. 如果依赖包缺失:")
    print("   pip install -r PyWxDump/requirements.txt")
    
    print("\n3. 如果目录不存在:")
    print("   mkdir data")
    print("   mkdir data\\csv")
    print("   mkdir data\\processed")
    
    print("\n4. 开始使用PyWxDump:")
    print("   a. 启动微信电脑版并登录")
    print("   b. 双击运行 '启动PyWxDump.bat'")
    print("   c. 在浏览器中访问 http://127.0.0.1:5000/")
    print("   d. 按照界面提示操作")
    
    print("\n5. 处理导出的CSV文件:")
    print("   python csv_processor.py")

def main():
    """主函数"""
    print("PyWxDump项目状态检查")
    print("="*50)
    
    checks = [
        check_python_version(),
        check_pywxdump_installation(),
        check_wxdump_command(),
        check_directory_structure(),
        check_required_files(),
        check_csv_files(),
        check_dependencies()
    ]
    
    passed_checks = sum(checks)
    total_checks = len(checks)
    
    print("\n" + "="*50)
    print("检查结果汇总:")
    print("="*50)
    print(f"通过检查: {passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("✓ 所有检查都通过！项目已准备就绪。")
        print("\n可以开始使用PyWxDump提取微信聊天记录了！")
    elif passed_checks >= total_checks - 2:
        print("⚠ 大部分检查通过，项目基本可用。")
        print("请查看上面的错误信息并进行相应修复。")
    else:
        print("✗ 多项检查未通过，需要进行配置。")
        print("请查看下面的建议进行修复。")
    
    provide_recommendations()

if __name__ == "__main__":
    main()
