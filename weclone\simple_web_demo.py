#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Web演示
内存友好的ChatGLM3模型演示
"""

import gradio as gr
import torch
from transformers import AutoTokenizer, AutoModel
import json
import os

class SimpleChatModel:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.loaded = False
        
    def load_model(self):
        """加载模型"""
        if self.loaded:
            return True
            
        try:
            print("正在加载模型...")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                "./chatglm3-6b", 
                trust_remote_code=True
            )
            
            # 加载模型（使用内存优化）
            self.model = AutoModel.from_pretrained(
                "./chatglm3-6b",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True,
                device_map="auto"
            ).eval()
            
            print("✅ 模型加载成功")
            self.loaded = True
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def chat(self, message, history):
        """聊天功能"""
        if not self.loaded:
            if not self.load_model():
                return "模型加载失败，请检查配置", history
        
        try:
            # 转换历史记录格式
            chat_history = []
            for h in history:
                if len(h) == 2:
                    chat_history.append([h[0], h[1]])
            
            # 生成回复
            response, new_history = self.model.chat(
                self.tokenizer, 
                message, 
                history=chat_history,
                max_length=2048,
                temperature=0.7
            )
            
            return response, new_history
            
        except Exception as e:
            error_msg = f"生成回复时出错: {str(e)}"
            print(error_msg)
            return error_msg, history

# 全局模型实例
chat_model = SimpleChatModel()

def chat_fn(message, history):
    """Gradio聊天函数"""
    if not message.strip():
        return history, ""
    
    try:
        response, new_history = chat_model.chat(message, history)
        
        # 更新历史记录
        history.append([message, response])
        
        return history, ""
        
    except Exception as e:
        error_response = f"出错了: {str(e)}"
        history.append([message, error_response])
        return history, ""

def load_training_examples():
    """加载训练样本作为示例"""
    try:
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            training_data = json.load(f)
        
        examples = []
        for sample in training_data[:3]:  # 只取前3个样本
            examples.append([sample["instruction"]])
        
        return examples
    except:
        return [
            ["你好"],
            ["今天天气怎么样？"],
            ["帮我写一首诗"]
        ]

def create_interface():
    """创建Gradio界面"""
    
    # 检查模型文件
    if not os.path.exists("./chatglm3-6b"):
        return gr.Interface(
            fn=lambda x: "❌ 模型文件不存在，请检查 ./chatglm3-6b 目录",
            inputs="text",
            outputs="text",
            title="模型文件缺失"
        )
    
    # 检查训练输出
    training_status = "✅ 已训练" if os.path.exists("./model_output") else "❌ 未训练"
    
    with gr.Blocks(title="ChatGLM3 简化演示") as demo:
        gr.Markdown(f"""
        # ChatGLM3 聊天演示
        
        **模型状态**: {training_status}
        **说明**: 这是一个简化的ChatGLM3演示界面
        
        """)
        
        with gr.Row():
            with gr.Column(scale=3):
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400,
                    show_label=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的消息...",
                        scale=4
                    )
                    send_btn = gr.Button("发送", scale=1, variant="primary")
                
                clear_btn = gr.Button("清除对话", variant="secondary")
                
            with gr.Column(scale=1):
                gr.Markdown("### 训练样本示例")
                examples = load_training_examples()
                gr.Examples(
                    examples=examples,
                    inputs=[msg],
                    label="点击试试这些例子"
                )
                
                gr.Markdown("### 模型信息")
                gr.Markdown("""
                - **模型**: ChatGLM3-6B
                - **微调方法**: LoRA
                - **训练数据**: 5个对话样本
                """)
        
        # 事件绑定
        send_btn.click(
            chat_fn,
            inputs=[msg, chatbot],
            outputs=[chatbot, msg]
        )
        
        msg.submit(
            chat_fn,
            inputs=[msg, chatbot],
            outputs=[chatbot, msg]
        )
        
        clear_btn.click(
            lambda: ([], ""),
            outputs=[chatbot, msg]
        )
    
    return demo

def main():
    """主函数"""
    print("启动ChatGLM3简化演示...")
    
    # 创建界面
    demo = create_interface()
    
    # 启动服务
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
