#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU状态检测脚本
检查系统GPU配置和PyTorch CUDA支持情况
"""

import subprocess
import platform
import sys

def check_nvidia_smi():
    """检查nvidia-smi命令"""
    print("=" * 50)
    print("1. 检查NVIDIA GPU驱动")
    print("=" * 50)
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA GPU驱动已安装")
            print(result.stdout)
            return True
        else:
            print("❌ nvidia-smi命令执行失败")
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi命令未找到 - 可能没有NVIDIA GPU或驱动未安装")
        return False
    except subprocess.TimeoutExpired:
        print("❌ nvidia-smi命令超时")
        return False
    except Exception as e:
        print(f"❌ 执行nvidia-smi时出错: {e}")
        return False

def check_pytorch_cuda():
    """检查PyTorch CUDA支持"""
    print("\n" + "=" * 50)
    print("2. 检查PyTorch CUDA支持")
    print("=" * 50)
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        cuda_available = torch.cuda.is_available()
        print(f"CUDA可用: {'✅ 是' if cuda_available else '❌ 否'}")
        
        if cuda_available:
            print(f"CUDA版本: {torch.version.cuda}")
            device_count = torch.cuda.device_count()
            print(f"GPU数量: {device_count}")
            
            for i in range(device_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_props = torch.cuda.get_device_properties(i)
                gpu_memory = gpu_props.total_memory / 1024**3
                print(f"GPU {i}: {gpu_name}")
                print(f"  显存: {gpu_memory:.1f} GB")
                print(f"  计算能力: {gpu_props.major}.{gpu_props.minor}")
                
                # 检查当前GPU内存使用情况
                if torch.cuda.is_available():
                    torch.cuda.set_device(i)
                    allocated = torch.cuda.memory_allocated(i) / 1024**3
                    cached = torch.cuda.memory_reserved(i) / 1024**3
                    print(f"  已分配内存: {allocated:.2f} GB")
                    print(f"  缓存内存: {cached:.2f} GB")
            
            return True
        else:
            print("❌ PyTorch无法使用CUDA")
            print("可能原因:")
            print("  - 没有NVIDIA GPU")
            print("  - CUDA驱动未安装")
            print("  - PyTorch版本不支持当前CUDA版本")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        print("请安装PyTorch: pip install torch")
        return False
    except Exception as e:
        print(f"❌ 检查PyTorch时出错: {e}")
        return False

def check_system_gpu():
    """检查系统GPU信息"""
    print("\n" + "=" * 50)
    print("3. 检查系统GPU信息")
    print("=" * 50)
    
    system = platform.system()
    print(f"操作系统: {system}")
    print(f"架构: {platform.machine()}")
    
    if system == "Windows":
        try:
            # Windows GPU信息
            result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("显卡列表:")
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # 跳过标题行
                    line = line.strip()
                    if line:
                        print(f"  - {line}")
            else:
                print("❌ 无法获取Windows GPU信息")
        except Exception as e:
            print(f"❌ 获取Windows GPU信息时出错: {e}")
            
    elif system == "Linux":
        try:
            # Linux GPU信息
            result = subprocess.run(['lspci', '|', 'grep', '-i', 'vga'], 
                                  shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("显卡列表:")
                print(result.stdout)
            else:
                print("❌ 无法获取Linux GPU信息")
        except Exception as e:
            print(f"❌ 获取Linux GPU信息时出错: {e}")
    
    elif system == "Darwin":  # macOS
        try:
            result = subprocess.run(['system_profiler', 'SPDisplaysDataType'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("显卡信息:")
                print(result.stdout)
            else:
                print("❌ 无法获取macOS GPU信息")
        except Exception as e:
            print(f"❌ 获取macOS GPU信息时出错: {e}")

def check_training_recommendation():
    """给出训练建议"""
    print("\n" + "=" * 50)
    print("4. 训练建议")
    print("=" * 50)
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            for i in range(gpu_count):
                gpu_props = torch.cuda.get_device_properties(i)
                gpu_memory = gpu_props.total_memory / 1024**3
                
                print(f"GPU {i} ({torch.cuda.get_device_name(i)}):")
                if gpu_memory >= 24:
                    print("  ✅ 显存充足，可以训练大模型")
                    print("  建议: batch_size=4-8, lora_rank=8-16")
                elif gpu_memory >= 12:
                    print("  ✅ 显存适中，可以训练中等模型")
                    print("  建议: batch_size=2-4, lora_rank=4-8")
                elif gpu_memory >= 6:
                    print("  ⚠️  显存较小，需要优化配置")
                    print("  建议: batch_size=1-2, lora_rank=2-4")
                else:
                    print("  ❌ 显存不足，建议使用CPU训练或更小模型")
                    print("  建议: batch_size=1, lora_rank=2")
        else:
            print("❌ 没有可用GPU，将使用CPU训练")
            print("CPU训练建议:")
            print("  - 使用更小的模型")
            print("  - batch_size=1")
            print("  - lora_rank=2")
            print("  - 减少训练数据量")
            print("  - 训练时间会很长，建议先用少量数据测试")
            
    except ImportError:
        print("❌ 无法导入PyTorch，请先安装")

def main():
    """主函数"""
    print("GPU状态检测脚本")
    print("作者: AI助手")
    print("用途: 检查GPU配置和PyTorch CUDA支持")
    
    # 检查各项配置
    nvidia_ok = check_nvidia_smi()
    pytorch_ok = check_pytorch_cuda()
    check_system_gpu()
    check_training_recommendation()
    
    # 总结
    print("\n" + "=" * 50)
    print("5. 检测总结")
    print("=" * 50)
    
    if nvidia_ok and pytorch_ok:
        print("✅ GPU配置完整，可以使用GPU训练")
    elif pytorch_ok:
        print("⚠️  PyTorch支持CUDA，但nvidia-smi不可用")
    else:
        print("❌ 建议使用CPU训练或配置GPU环境")
    
    print("\n运行完成！")

if __name__ == "__main__":
    main()
