#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
验证训练效果的最简单方法
"""

import torch
from transformers import AutoTokenizer, AutoModel
from peft import PeftModel
import json

def test_model():
    """测试模型"""
    print("=" * 50)
    print("ChatGLM3 训练效果测试")
    print("=" * 50)
    
    try:
        print("1. 加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained("./chatglm3-6b", trust_remote_code=True)
        print("✅ 分词器加载成功")
        
        print("2. 加载基础模型...")
        base_model = AutoModel.from_pretrained(
            "./chatglm3-6b",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        print("✅ 基础模型加载成功")
        
        print("3. 加载LoRA适配器...")
        model = PeftModel.from_pretrained(base_model, "./model_output")
        model.eval()
        print("✅ LoRA适配器加载成功")
        
        print("4. 读取训练数据...")
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            training_data = json.load(f)
        print(f"✅ 训练数据加载成功 ({len(training_data)} 个样本)")
        
        print("\n" + "=" * 50)
        print("开始测试训练效果")
        print("=" * 50)
        
        # 测试训练样本
        for i, sample in enumerate(training_data, 1):
            input_text = sample["instruction"]
            expected_output = sample["output"]
            
            print(f"\n--- 测试样本 {i} ---")
            print(f"输入: {input_text}")
            print(f"期望输出: {expected_output}")
            
            try:
                # 尝试对话
                response, history = model.chat(tokenizer, input_text, history=[])
                print(f"模型输出: {response}")
                
                # 简单的相似度判断
                if any(word in response for word in expected_output.split()[:3]):
                    print("✅ 输出包含期望内容")
                else:
                    print("⚠️  输出与期望不同")
                    
            except Exception as e:
                print(f"❌ 生成失败: {e}")
        
        print("\n" + "=" * 50)
        print("交互测试")
        print("=" * 50)
        print("输入 'quit' 退出")
        
        history = []
        while True:
            try:
                user_input = input("\n你: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                
                response, history = model.chat(tokenizer, user_input, history=history)
                print(f"模型: {response}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"错误: {e}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_training_results():
    """检查训练结果"""
    print("=" * 50)
    print("训练结果检查")
    print("=" * 50)
    
    try:
        with open("./model_output/train_results.json", "r") as f:
            results = json.load(f)
        
        print("训练指标:")
        for key, value in results.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.6f}")
            else:
                print(f"  {key}: {value}")
        
        # 判断训练效果
        if "train_loss" in results:
            loss = results["train_loss"]
            if loss < 1.5:
                print(f"\n✅ 训练损失 {loss:.4f} 很好！")
            elif loss < 3.0:
                print(f"\n⚠️  训练损失 {loss:.4f} 一般")
            else:
                print(f"\n❌ 训练损失 {loss:.4f} 较高")
        
    except Exception as e:
        print(f"❌ 无法读取训练结果: {e}")

def main():
    """主函数"""
    print("ChatGLM3 最终测试脚本")
    
    # 检查训练结果
    check_training_results()
    
    print("\n是否要测试模型对话？(y/n): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes', '是']:
        test_model()
    else:
        print("跳过模型测试")

if __name__ == "__main__":
    main()
