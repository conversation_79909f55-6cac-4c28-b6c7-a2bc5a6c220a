{"added_tokens_decoder": {"64790": {"content": "[gMASK]", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "64792": {"content": "sop", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "64795": {"content": "<|user|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "64796": {"content": "<|assistant|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "64797": {"content": "<|observation|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<|user|>", "<|observation|>"], "auto_map": {"AutoTokenizer": ["tokenization_chatglm.ChatGLMTokenizer", null]}, "chat_template": "{% for message in messages %}{% if loop.first %}[gMASK]sop<|{{ message['role'] }}|>\n {{ message['content'] }}{% else %}<|{{ message['role'] }}|>\n {{ message['content'] }}{% endif %}{% endfor %}{% if add_generation_prompt %}<|assistant|>{% endif %}", "clean_up_tokenization_spaces": false, "do_lower_case": false, "eos_token": "</s>", "model_max_length": 1000000000000000019884624838656, "pad_token": "<unk>", "padding_side": "right", "remove_space": false, "split_special_tokens": false, "tokenizer_class": "ChatGLMTokenizer", "unk_token": "<unk>"}