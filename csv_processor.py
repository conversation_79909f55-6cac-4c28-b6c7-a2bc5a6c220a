#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV聊天记录处理工具
用于处理从PyWxDump导出的CSV聊天记录文件
"""

import os
import csv
import json
import shutil
from datetime import datetime
from pathlib import Path

class CSVProcessor:
    def __init__(self, data_dir="./data"):
        """
        初始化CSV处理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.csv_dir = self.data_dir / "csv"
        self.output_dir = self.data_dir / "processed"
        
        # 创建必要的目录
        self.csv_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def copy_exported_csv(self, source_dir="wxdump_tmp/export"):
        """
        从PyWxDump导出目录复制CSV文件到data/csv目录
        
        Args:
            source_dir: PyWxDump导出目录路径
        """
        source_path = Path(source_dir)
        
        if not source_path.exists():
            print(f"错误：导出目录 {source_dir} 不存在")
            print("请先使用PyWxDump导出聊天记录为CSV格式")
            return False
        
        print(f"正在从 {source_dir} 复制CSV文件...")
        
        # 查找所有CSV文件
        csv_files = list(source_path.glob("**/*.csv"))
        
        if not csv_files:
            print(f"在 {source_dir} 中没有找到CSV文件")
            return False
        
        copied_count = 0
        for csv_file in csv_files:
            try:
                # 复制文件到目标目录
                dest_file = self.csv_dir / csv_file.name
                shutil.copy2(csv_file, dest_file)
                print(f"已复制: {csv_file.name}")
                copied_count += 1
            except Exception as e:
                print(f"复制文件 {csv_file.name} 时出错: {e}")
        
        print(f"成功复制 {copied_count} 个CSV文件到 {self.csv_dir}")
        return True
    
    def list_csv_files(self):
        """
        列出data/csv目录中的所有CSV文件
        
        Returns:
            list: CSV文件路径列表
        """
        csv_files = list(self.csv_dir.glob("*.csv"))
        
        if not csv_files:
            print(f"在 {self.csv_dir} 中没有找到CSV文件")
            print("请先使用 copy_exported_csv() 方法复制导出的CSV文件")
            return []
        
        print(f"找到 {len(csv_files)} 个CSV文件:")
        for i, csv_file in enumerate(csv_files, 1):
            print(f"{i}. {csv_file.name}")
        
        return csv_files
    
    def analyze_csv_file(self, csv_file_path):
        """
        分析单个CSV文件的内容
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            dict: 分析结果
        """
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            if not rows:
                return {"error": "CSV文件为空"}
            
            # 基本统计信息
            total_messages = len(rows)
            
            # 获取字段名
            fieldnames = list(rows[0].keys()) if rows else []
            
            # 统计发送者
            senders = {}
            for row in rows:
                sender = row.get('发送者', row.get('sender', '未知'))
                senders[sender] = senders.get(sender, 0) + 1
            
            # 时间范围（如果有时间字段）
            time_range = None
            time_field = None
            for field in ['时间', 'time', 'timestamp', '发送时间']:
                if field in fieldnames:
                    time_field = field
                    break
            
            if time_field:
                times = [row[time_field] for row in rows if row[time_field]]
                if times:
                    time_range = {"start": min(times), "end": max(times)}
            
            return {
                "file_name": Path(csv_file_path).name,
                "total_messages": total_messages,
                "fieldnames": fieldnames,
                "senders": senders,
                "time_range": time_range,
                "sample_data": rows[:3] if len(rows) >= 3 else rows
            }
            
        except Exception as e:
            return {"error": f"分析文件时出错: {e}"}
    
    def generate_summary_report(self):
        """
        生成所有CSV文件的汇总报告
        """
        csv_files = self.list_csv_files()
        
        if not csv_files:
            return
        
        print("\n" + "="*50)
        print("聊天记录汇总报告")
        print("="*50)
        
        total_files = len(csv_files)
        total_messages = 0
        all_senders = set()
        
        report_data = []
        
        for csv_file in csv_files:
            print(f"\n分析文件: {csv_file.name}")
            analysis = self.analyze_csv_file(csv_file)
            
            if "error" in analysis:
                print(f"  错误: {analysis['error']}")
                continue
            
            print(f"  消息数量: {analysis['total_messages']}")
            print(f"  参与者: {list(analysis['senders'].keys())}")
            
            if analysis['time_range']:
                print(f"  时间范围: {analysis['time_range']['start']} 到 {analysis['time_range']['end']}")
            
            total_messages += analysis['total_messages']
            all_senders.update(analysis['senders'].keys())
            report_data.append(analysis)
        
        print(f"\n" + "="*50)
        print("总体统计:")
        print(f"  文件总数: {total_files}")
        print(f"  消息总数: {total_messages}")
        print(f"  参与者总数: {len(all_senders)}")
        print(f"  所有参与者: {list(all_senders)}")
        
        # 保存详细报告到JSON文件
        report_file = self.output_dir / f"chat_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: {report_file}")

def main():
    """
    主函数 - 提供交互式菜单
    """
    processor = CSVProcessor()
    
    while True:
        print("\n" + "="*40)
        print("PyWxDump CSV处理工具")
        print("="*40)
        print("1. 从导出目录复制CSV文件")
        print("2. 列出现有CSV文件")
        print("3. 生成汇总报告")
        print("4. 退出")
        print("-"*40)
        
        choice = input("请选择操作 (1-4): ").strip()
        
        if choice == "1":
            source_dir = input("请输入PyWxDump导出目录路径 (默认: wxdump_tmp/export): ").strip()
            if not source_dir:
                source_dir = "wxdump_tmp/export"
            processor.copy_exported_csv(source_dir)
            
        elif choice == "2":
            processor.list_csv_files()
            
        elif choice == "3":
            processor.generate_summary_report()
            
        elif choice == "4":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
