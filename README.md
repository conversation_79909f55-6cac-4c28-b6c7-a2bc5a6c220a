# 微信聊天记录提取项目

本项目基于 [PyWxDump](https://github.com/xaoyaoo/PyWxDump) 工具，用于提取和处理微信聊天记录。

## 项目结构

```
项目根目录/
├── PyWxDump/                 # PyWxDump源码目录
├── data/                     # 数据存储目录
│   ├── csv/                 # CSV格式聊天记录
│   └── processed/           # 处理后的数据
├── weclone/                 # WeClone相关文件
├── wxdump_web/              # Web界面文件
├── 启动PyWxDump.bat         # Windows启动脚本
├── csv_processor.py         # CSV文件处理工具
├── PyWxDump使用指南.md      # 详细使用指南
└── README.md               # 本文件
```

## 快速开始

### 1. 环境准备
- Windows 10 64位及以上
- Python 3.8及以上
- 微信电脑版

### 2. 启动微信
确保微信电脑版已启动并登录要提取聊天记录的账号。

### 3. 启动PyWxDump
双击运行 `启动PyWxDump.bat` 或在命令行中执行：
```bash
wxdump ui
```

### 4. 使用Web界面
在浏览器中访问 http://127.0.0.1:5000/ 进行操作：
1. 获取微信信息
2. 解密数据库
3. 导出聊天记录为CSV格式

### 5. 处理CSV文件
使用提供的Python工具处理导出的CSV文件：
```bash
python csv_processor.py
```

## 详细使用说明

请查看 [PyWxDump使用指南.md](./PyWxDump使用指南.md) 获取详细的使用步骤和说明。

## 主要功能

### PyWxDump功能
- 获取微信账户信息（昵称、账号、手机号、邮箱、数据库密钥）
- 解密微信数据库
- 通过Web界面查看聊天记录
- 导出聊天记录为HTML、CSV等格式
- 支持远程访问（局域网）

### CSV处理工具功能
- 自动复制导出的CSV文件到指定目录
- 分析CSV文件内容和统计信息
- 生成聊天记录汇总报告
- 支持批量处理多个聊天记录文件

## 使用流程

1. **准备阶段**
   - 启动微信电脑版并登录
   - 确保有管理员权限

2. **提取阶段**
   - 运行PyWxDump获取微信信息
   - 解密微信数据库
   - 导出聊天记录为CSV格式

3. **处理阶段**
   - 将导出的CSV文件复制到 `./data/csv/` 目录
   - 使用CSV处理工具分析和整理数据

## 注意事项

### 安全提醒
- 本工具仅用于个人聊天记录备份
- 请勿用于非法目的或窃取他人隐私
- 导出的数据包含敏感信息，请妥善保管

### 技术要求
- 需要管理员权限运行
- 确保微信版本受支持
- 建议在安全环境中使用

### 常见问题
- 如果无法获取微信信息，请检查微信是否正常运行
- 如果解密失败，请重新获取微信信息
- 如果Web界面无法访问，请检查防火墙设置

## 相关链接

- [PyWxDump官方仓库](https://github.com/xaoyaoo/PyWxDump)
- [PyWxDump Mini版本](https://github.com/xaoyaoo/pywxdumpmini)
- [Web界面仓库](https://github.com/xaoyaoo/wxdump_web)

## 免责声明

本项目仅供学习和个人备份使用，请遵守相关法律法规，不得用于非法用途。使用本工具产生的任何后果由使用者自行承担。

## 技术支持

如遇到问题，请：
1. 查看 [PyWxDump使用指南.md](./PyWxDump使用指南.md)
2. 查看PyWxDump官方文档和FAQ
3. 在GitHub上提交Issue

---

**重要提醒：使用前请仔细阅读免责声明，确保合法合规使用！**
