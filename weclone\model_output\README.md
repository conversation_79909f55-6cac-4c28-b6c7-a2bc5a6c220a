---
library_name: peft
license: other
base_model: ./chatglm3-6b
tags:
- llama-factory
- lora
- generated_from_trainer
model-index:
- name: model_output
  results: []
---

<!-- This model card has been generated automatically according to the information the Trainer had access to. You
should probably proofread and complete it, then remove this comment. -->

# model_output

This model is a fine-tuned version of [./chatglm3-6b](https://huggingface.co/./chatglm3-6b) on the wechat-sft dataset.

## Model description

More information needed

## Intended uses & limitations

More information needed

## Training and evaluation data

More information needed

## Training procedure

### Training hyperparameters

The following hyperparameters were used during training:
- learning_rate: 0.0001
- train_batch_size: 4
- eval_batch_size: 8
- seed: 42
- gradient_accumulation_steps: 8
- total_train_batch_size: 32
- optimizer: Adam with betas=(0.9,0.999) and epsilon=1e-08
- lr_scheduler_type: cosine
- num_epochs: 3
- mixed_precision_training: Native AMP

### Training results



### Framework versions

- PEFT 0.15.2
- Transformers 4.45.2
- Pytorch 2.7.1+cu126
- Datasets 3.6.0
- Tokenizers 0.20.3