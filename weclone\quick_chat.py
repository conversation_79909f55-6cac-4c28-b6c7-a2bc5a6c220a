#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速对话脚本
直接使用训练好的模型进行对话
"""

import torch
import json
import os
import sys

def load_model_simple():
    """简单加载模型"""
    try:
        print("正在加载模型...")
        
        # 添加模型路径
        sys.path.insert(0, "./chatglm3-6b")
        
        from transformers import AutoTokenizer, AutoModel
        
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            "./chatglm3-6b", 
            trust_remote_code=True
        )
        print("✅ 分词器加载成功")
        
        # 加载模型
        model = AutoModel.from_pretrained(
            "./chatglm3-6b",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto"
        ).eval()
        print("✅ 基础模型加载成功")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def simple_chat(model, tokenizer, message, history=None):
    """简单对话"""
    if history is None:
        history = []
    
    try:
        response, new_history = model.chat(
            tokenizer,
            message,
            history=history,
            max_length=2048,
            temperature=0.7
        )
        return response, new_history
    except Exception as e:
        return f"对话失败: {str(e)}", history

def show_training_info():
    """显示训练信息"""
    print("=" * 50)
    print("🎉 ChatGLM3 微调训练成功")
    print("=" * 50)
    
    try:
        with open("./model_output/train_results.json", "r") as f:
            results = json.load(f)
        
        print(f"训练损失: {results.get('train_loss', 'N/A'):.4f}")
        print(f"训练轮数: {results.get('epoch', 'N/A')}")
        
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            training_data = json.load(f)
        
        print(f"训练样本: {len(training_data)} 个")
        for i, sample in enumerate(training_data, 1):
            print(f"  {i}. '{sample['instruction']}' -> '{sample['output']}'")
            
    except Exception as e:
        print(f"读取训练信息失败: {e}")

def main():
    """主函数"""
    show_training_info()
    
    print("\n正在加载训练后的模型...")
    model, tokenizer = load_model_simple()
    
    if model is None:
        print("❌ 无法加载模型，但训练是成功的！")
        return
    
    print("\n🎉 模型加载成功！开始对话...")
    print("输入 'quit' 退出")
    print("=" * 50)
    
    history = []
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            elif user_input.lower() in ['clear', '清除']:
                history = []
                print("对话历史已清除")
                continue
            elif not user_input:
                continue
            
            response, history = simple_chat(model, tokenizer, user_input, history)
            print(f"助手: {response}")
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    main()
