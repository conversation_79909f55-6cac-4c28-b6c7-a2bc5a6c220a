#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的模型测试脚本
测试训练好的ChatGLM3模型
"""

import torch
from transformers import AutoTokenizer, AutoModel
import json
import os

def load_model_and_tokenizer():
    """加载模型和分词器"""
    print("正在加载模型和分词器...")

    try:
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained("./chatglm3-6b", trust_remote_code=True)
        print("✅ 分词器加载成功")

        # 加载基础模型
        model = AutoModel.from_pretrained("./chatglm3-6b", trust_remote_code=True, torch_dtype=torch.float16)
        print("✅ 基础模型加载成功")

        # 设置为评估模式
        model = model.eval()

        print("✅ 模型加载完成！")
        return model, tokenizer

    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def chat_with_model(model, tokenizer, query, history=None):
    """与模型对话"""
    if history is None:
        history = []

    try:
        print(f"🤖 正在处理: {query}")
        # 使用模型的chat方法
        response, history = model.chat(tokenizer, query, history=history)
        return response, history
    except Exception as e:
        print(f"❌ 对话时出错: {e}")
        print(f"错误类型: {type(e).__name__}")
        return None, history

def test_training_data(model, tokenizer):
    """测试训练数据中的样本"""
    print("\n" + "="*50)
    print("测试训练数据样本")
    print("="*50)
    
    # 读取训练数据
    try:
        with open('./data/res_csv/sft/sft-my.json', 'r', encoding='utf-8') as f:
            training_data = json.load(f)
        
        print(f"找到 {len(training_data)} 个训练样本")
        
        for i, sample in enumerate(training_data):
            instruction = sample['instruction']
            expected_output = sample['output']
            
            print(f"\n--- 测试样本 {i+1} ---")
            print(f"输入: {instruction}")
            print(f"期望输出: {expected_output}")
            
            # 测试模型回复
            response, _ = chat_with_model(model, tokenizer, instruction)
            if response:
                print(f"模型输出: {response}")
                
                # 简单的相似度检查
                if expected_output.lower() in response.lower() or response.lower() in expected_output.lower():
                    print("✅ 输出相似")
                else:
                    print("❌ 输出不同")
            else:
                print("❌ 模型无法生成回复")
                
    except Exception as e:
        print(f"读取训练数据时出错: {e}")

def interactive_chat(model, tokenizer):
    """交互式对话"""
    print("\n" + "="*50)
    print("交互式对话模式")
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'clear' 清除对话历史")
    print("="*50)
    
    history = []
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            elif user_input.lower() in ['clear', '清除']:
                history = []
                print("对话历史已清除")
                continue
            elif not user_input:
                continue
            
            # 与模型对话
            response, history = chat_with_model(model, tokenizer, user_input, history)
            
            if response:
                print(f"模型: {response}")
            else:
                print("模型: [无法生成回复]")
                
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"对话出错: {e}")

def main():
    """主函数"""
    print("ChatGLM3 模型测试脚本")
    print("="*50)

    try:
        # 检查模型文件
        if not os.path.exists("./chatglm3-6b"):
            print("❌ 找不到模型目录 ./chatglm3-6b")
            return

        if not os.path.exists("./model_output"):
            print("❌ 找不到训练输出目录 ./model_output")
            print("请先运行训练脚本")
            return

        # 加载模型
        model, tokenizer = load_model_and_tokenizer()

        if model is None or tokenizer is None:
            print("❌ 模型加载失败，退出程序")
            return

        # 测试训练数据
        test_training_data(model, tokenizer)

        # 交互式对话
        interactive_chat(model, tokenizer)

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("可能的解决方案:")
        print("1. 检查模型文件是否完整")
        print("2. 检查Python环境和依赖")
        print("3. 尝试重新训练模型")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
