#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可工作的ChatGLM3演示
使用兼容的方式加载和推理
"""

import gradio as gr
import torch
import json
import os
from transformers import AutoTokenizer, AutoModel
from peft import PeftModel

class WorkingChatModel:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.loaded = False
        
    def load_model(self):
        """加载模型（兼容方式）"""
        if self.loaded:
            return True
            
        try:
            print("正在加载模型...")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                "./chatglm3-6b", 
                trust_remote_code=True,
                padding_side='left'  # 明确指定padding方向
            )
            
            # 加载基础模型
            base_model = AutoModel.from_pretrained(
                "./chatglm3-6b",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            
            # 加载LoRA适配器
            if os.path.exists("./model_output"):
                print("正在加载LoRA适配器...")
                self.model = PeftModel.from_pretrained(base_model, "./model_output")
                print("✅ LoRA适配器加载成功")
            else:
                self.model = base_model
                print("⚠️  未找到LoRA适配器，使用基础模型")
            
            self.model.eval()
            print("✅ 模型加载成功")
            self.loaded = True
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def simple_generate(self, text, max_length=100):
        """简单的文本生成"""
        try:
            # 编码输入
            inputs = self.tokenizer.encode(text, return_tensors="pt")
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + max_length,
                    num_return_sequences=1,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            response = self.tokenizer.decode(
                outputs[0][inputs.shape[1]:], 
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            return f"生成失败: {str(e)}"

# 全局模型实例
chat_model = WorkingChatModel()

def chat_fn(message, history):
    """聊天函数"""
    if not message.strip():
        return history, ""
    
    # 确保模型已加载
    if not chat_model.loaded:
        if not chat_model.load_model():
            history.append([message, "❌ 模型加载失败"])
            return history, ""
    
    try:
        # 生成回复
        response = chat_model.simple_generate(message, max_length=50)
        
        # 添加到历史
        history.append([message, response])
        
        return history, ""
        
    except Exception as e:
        error_msg = f"生成失败: {str(e)}"
        history.append([message, error_msg])
        return history, ""

def load_training_info():
    """加载训练信息"""
    info = "## 训练信息\n\n"
    
    # 训练结果
    try:
        with open("./model_output/train_results.json", "r") as f:
            results = json.load(f)
        info += "### 训练结果\n"
        for key, value in results.items():
            if isinstance(value, float):
                info += f"- **{key}**: {value:.6f}\n"
            else:
                info += f"- **{key}**: {value}\n"
        info += "\n"
    except:
        info += "### 训练结果\n❌ 无法读取训练结果\n\n"
    
    # 训练数据
    try:
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        info += f"### 训练数据 ({len(data)} 个样本)\n"
        for i, sample in enumerate(data[:3], 1):  # 只显示前3个
            info += f"**样本 {i}**:\n"
            info += f"- 输入: {sample['instruction']}\n"
            info += f"- 输出: {sample['output']}\n\n"
    except:
        info += "### 训练数据\n❌ 无法读取训练数据\n\n"
    
    return info

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="ChatGLM3 可工作演示") as demo:
        gr.Markdown("# ChatGLM3 训练后模型演示")
        gr.Markdown("这个版本使用兼容的方式加载模型，应该可以正常工作")
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="对话",
                    height=400,
                    type="tuples"  # 明确指定类型
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入",
                        placeholder="请输入消息...",
                        scale=4
                    )
                    send_btn = gr.Button("发送", scale=1, variant="primary")
                
                clear_btn = gr.Button("清除", variant="secondary")
                
                # 测试按钮
                with gr.Row():
                    test1_btn = gr.Button("测试: 是耶，挺好。", variant="outline")
                    test2_btn = gr.Button("测试: 哪天不hot。", variant="outline")
            
            with gr.Column(scale=1):
                info_display = gr.Markdown(load_training_info())
                
                status_display = gr.Markdown("### 模型状态\n等待加载...")
                
                load_btn = gr.Button("重新加载模型", variant="secondary")
        
        # 事件绑定
        send_btn.click(
            chat_fn,
            inputs=[msg, chatbot],
            outputs=[chatbot, msg]
        )
        
        msg.submit(
            chat_fn,
            inputs=[msg, chatbot],
            outputs=[chatbot, msg]
        )
        
        clear_btn.click(
            lambda: ([], ""),
            outputs=[chatbot, msg]
        )
        
        # 测试按钮
        test1_btn.click(
            lambda: chat_fn("是耶，挺好。", []),
            outputs=[chatbot, msg]
        )
        
        test2_btn.click(
            lambda: chat_fn("哪天不hot。", []),
            outputs=[chatbot, msg]
        )
        
        # 加载模型按钮
        def load_model_status():
            if chat_model.load_model():
                return "### 模型状态\n✅ 模型加载成功\n- 基础模型: ChatGLM3-6B\n- LoRA适配器: 已加载"
            else:
                return "### 模型状态\n❌ 模型加载失败"
        
        load_btn.click(
            load_model_status,
            outputs=status_display
        )
    
    return demo

def main():
    """主函数"""
    print("启动可工作的ChatGLM3演示...")
    
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
