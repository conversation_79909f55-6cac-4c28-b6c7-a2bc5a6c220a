{"base_model.model.transformer.encoder.layers.5.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.5.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.5.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.5.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.5.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.5.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.5.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.5.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.5.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.6.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.6.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.6.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.6.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.6.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.6.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.6.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.6.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.6.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.7.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.7.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.7.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.7.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.7.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.7.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.7.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.7.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.7.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.8.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.8.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.8.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.8.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.8.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.8.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.8.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.8.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.8.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.9.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.9.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.9.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.9.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.9.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.9.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.9.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.9.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.9.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.10.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.10.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.10.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.10.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.10.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.10.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.10.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.10.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.10.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.11.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.11.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.11.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.11.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.11.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.11.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.11.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.11.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.11.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.12.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.12.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.12.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.12.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.12.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.12.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.12.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.12.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.12.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.13.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.13.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.13.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.13.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.13.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.13.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.13.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.13.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.13.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.14.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.14.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.14.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.14.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.14.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.14.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.14.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.14.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.14.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.15.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.15.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.15.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.15.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.15.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.15.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.15.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.15.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.15.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.16.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.16.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.16.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.16.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.16.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.16.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.16.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.16.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.16.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.17.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.17.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.17.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.17.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.17.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.17.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.17.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.17.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.17.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.18.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.18.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.18.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.18.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.18.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.18.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.18.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.18.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.18.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.19.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.19.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.19.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.19.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.19.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.19.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.19.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.19.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.19.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.20.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.20.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.20.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.20.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.20.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.20.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.20.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.20.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.20.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.21.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.21.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.21.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.21.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.21.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.21.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.21.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.21.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.21.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.22.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.22.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.22.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.22.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.22.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.22.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.22.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.22.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.22.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.23.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.23.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.23.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.23.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.23.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.23.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.23.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.23.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.23.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.24.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.24.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.24.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.24.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.24.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.24.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.24.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.24.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.24.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.25.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.25.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.25.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.25.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.25.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.25.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.25.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.25.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.25.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.26.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.26.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.26.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.26.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.26.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.26.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.26.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.26.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.26.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.layers.27.input_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.27.self_attention.query_key_value.base_layer.weight": {"dtype": "float16", "shape": [4608, 4096]}, "base_model.model.transformer.encoder.layers.27.self_attention.query_key_value.base_layer.bias": {"dtype": "float16", "shape": [4608]}, "base_model.model.transformer.encoder.layers.27.self_attention.query_key_value.lora_A.default.weight": {"dtype": "float32", "shape": [4, 4096]}, "base_model.model.transformer.encoder.layers.27.self_attention.query_key_value.lora_B.default.weight": {"dtype": "float32", "shape": [4608, 4]}, "base_model.model.transformer.encoder.layers.27.self_attention.dense.weight": {"dtype": "float16", "shape": [4096, 4096]}, "base_model.model.transformer.encoder.layers.27.post_attention_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.encoder.layers.27.mlp.dense_h_to_4h.weight": {"dtype": "float16", "shape": [27392, 4096]}, "base_model.model.transformer.encoder.layers.27.mlp.dense_4h_to_h.weight": {"dtype": "float16", "shape": [4096, 13696]}, "base_model.model.transformer.encoder.final_layernorm.weight": {"dtype": "float16", "shape": [4096]}, "base_model.model.transformer.output_layer.weight": {"dtype": "float16", "shape": [65024, 4096]}}