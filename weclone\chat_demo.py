#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatGLM3 对话演示
使用兼容版本的transformers
"""

import torch
from transformers import AutoTokenizer, AutoModel
from peft import PeftModel
import json

class ChatGLM3Demo:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.loaded = False
        
    def load_model(self):
        """加载模型"""
        if self.loaded:
            return True
            
        try:
            print("正在加载ChatGLM3模型...")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                "./chatglm3-6b", 
                trust_remote_code=True
            )
            print("✅ 分词器加载成功")
            
            # 加载基础模型
            base_model = AutoModel.from_pretrained(
                "./chatglm3-6b",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            print("✅ 基础模型加载成功")
            
            # 加载LoRA适配器
            self.model = PeftModel.from_pretrained(base_model, "./model_output")
            self.model.eval()
            print("✅ LoRA适配器加载成功")
            
            self.loaded = True
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def chat(self, message, history=None):
        """对话功能"""
        if not self.loaded:
            if not self.load_model():
                return "模型加载失败", []
        
        if history is None:
            history = []
            
        try:
            response, new_history = self.model.chat(
                self.tokenizer,
                message,
                history=history,
                max_length=2048,
                temperature=0.7
            )
            return response, new_history
            
        except Exception as e:
            return f"对话失败: {str(e)}", history

def test_training_samples():
    """测试训练样本"""
    print("=" * 50)
    print("测试训练样本")
    print("=" * 50)
    
    # 读取训练数据
    try:
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            training_data = json.load(f)
    except:
        print("❌ 无法读取训练数据")
        return
    
    demo = ChatGLM3Demo()
    
    for i, sample in enumerate(training_data, 1):
        input_text = sample["instruction"]
        expected_output = sample["output"]
        
        print(f"\n--- 样本 {i} ---")
        print(f"输入: {input_text}")
        print(f"期望: {expected_output}")
        
        response, _ = demo.chat(input_text)
        print(f"输出: {response}")
        
        # 简单相似度检查
        if any(word in response for word in expected_output.split()[:3]):
            print("✅ 输出相关")
        else:
            print("⚠️  输出不同")

def interactive_chat():
    """交互式对话"""
    print("\n" + "=" * 50)
    print("交互式对话")
    print("输入 'quit' 退出")
    print("=" * 50)
    
    demo = ChatGLM3Demo()
    history = []
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            elif user_input.lower() in ['clear', '清除']:
                history = []
                print("对话历史已清除")
                continue
            elif not user_input:
                continue
            
            response, history = demo.chat(user_input, history)
            print(f"助手: {response}")
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"错误: {e}")

def main():
    """主函数"""
    print("ChatGLM3 训练后对话演示")
    print("=" * 50)
    
    # 显示训练信息
    try:
        with open("./model_output/train_results.json", "r") as f:
            results = json.load(f)
        print(f"训练损失: {results.get('train_loss', 'N/A'):.4f}")
        print(f"训练轮数: {results.get('epoch', 'N/A')}")
    except:
        print("无法读取训练结果")
    
    print("\n选择测试模式:")
    print("1. 测试训练样本")
    print("2. 交互式对话")
    print("3. 两者都测试")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        test_training_samples()
    elif choice == "2":
        interactive_chat()
    elif choice == "3":
        test_training_samples()
        interactive_chat()
    else:
        print("无效选择，默认进入交互模式")
        interactive_chat()

if __name__ == "__main__":
    main()
