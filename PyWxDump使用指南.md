# PyWxDump 微信聊天记录提取使用指南

## 概述
PyWxDump 是一个用于提取微信聊天记录的工具，可以获取微信账户信息、解密数据库、查看微信聊天记录并导出为多种格式。

## 环境要求
- Windows 10 64位及以上
- Python 3.8及以上
- 微信电脑版

## 安装步骤

### 1. 确认PyWxDump已安装
PyWxDump已经安装在当前项目中，可以直接使用。

### 2. 目录结构
```
项目根目录/
├── PyWxDump/           # PyWxDump源码
├── data/               # 数据目录
│   └── csv/           # CSV导出文件存放目录
└── PyWxDump使用指南.md  # 本指南
```

## 使用步骤

### 第一步：启动微信电脑版
1. 打开微信电脑版
2. 确保已经登录你要提取聊天记录的微信账号

### 第二步：启动PyWxDump图形界面
在项目根目录下运行以下命令：
```bash
wxdump ui
```
这将启动Web图形界面，默认地址为：http://127.0.0.1:5000/

### 第三步：获取微信信息和解密数据库
1. 在浏览器中打开 http://127.0.0.1:5000/
2. 点击"获取微信信息"按钮
3. 系统会自动获取当前登录微信的信息（昵称、账号、手机号、密钥等）
4. 点击"解密数据库"按钮，系统会自动解密微信数据库

### 第四步：导出聊天记录为CSV
1. 在图形界面中点击"聊天备份"
2. 选择"导出类型"为"CSV"
3. 选择要导出的联系人或群聊（可以选择多个）
4. 点击"开始导出"
5. 导出的文件会保存在 `wxdump_tmp/export` 目录中

### 第五步：整理CSV文件
1. 找到导出的CSV文件，位置在：`wxdump_tmp/export`
2. 将整个CSV文件夹复制到项目的 `./data/csv` 目录中
3. 最终目录结构应该是：
   ```
   ./data/csv/
   ├── 联系人1_聊天记录.csv
   ├── 联系人2_聊天记录.csv
   ├── 群聊1_聊天记录.csv
   └── ...
   ```

## 命令行使用方法

### 查看帮助
```bash
wxdump -h
```

### 获取微信信息
```bash
wxdump info
```

### 解密数据库
```bash
wxdump decrypt -k <密钥> -i <数据库路径> -o <输出路径>
```

### 启动图形界面
```bash
wxdump ui
```

### 启动API服务
```bash
wxdump api
```

## 常见问题

### 1. 无法获取微信信息
- 确保微信电脑版已经启动并登录
- 确保微信版本受支持
- 尝试以管理员权限运行

### 2. 数据库解密失败
- 确保获取到了正确的密钥
- 确保微信数据库文件没有被占用
- 重新获取微信信息

### 3. 导出CSV失败
- 确保数据库已经成功解密
- 检查磁盘空间是否充足
- 确保有足够的权限写入文件

### 4. 图形界面无法访问
- 检查防火墙设置
- 确保端口5000没有被占用
- 尝试使用 `127.0.0.1:5000` 而不是 `localhost:5000`

## 注意事项

### 法律声明
- 本工具仅用于学习和个人备份目的
- 请勿用于非法用途
- 请勿用于窃取他人隐私
- 使用本工具即表示同意相关免责声明

### 安全提醒
- 导出的聊天记录包含敏感信息，请妥善保管
- 建议在安全的环境中使用
- 定期清理临时文件

### 技术支持
- 官方GitHub：https://github.com/xaoyaoo/PyWxDump
- 详细文档：查看PyWxDump/doc目录下的文档
- 常见问题：查看PyWxDump/doc/FAQ.md

## 下一步
完成CSV文件导出后，您可以：
1. 使用Excel或其他工具分析聊天数据
2. 开发自定义的数据处理程序
3. 将数据导入到数据库中进行进一步分析
