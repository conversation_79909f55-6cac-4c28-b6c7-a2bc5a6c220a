#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级演示
仅验证训练结果，不加载完整模型
"""

import gradio as gr
import json
import os
import torch
from safetensors import safe_open

def analyze_training_results():
    """分析训练结果"""
    results = {}
    
    # 检查训练文件
    if os.path.exists("./model_output/train_results.json"):
        with open("./model_output/train_results.json", "r") as f:
            train_results = json.load(f)
        results["训练结果"] = train_results
    
    # 检查LoRA配置
    if os.path.exists("./model_output/adapter_config.json"):
        with open("./model_output/adapter_config.json", "r") as f:
            adapter_config = json.load(f)
        results["LoRA配置"] = adapter_config
    
    # 检查训练数据
    if os.path.exists("./data/res_csv/sft/sft-my.json"):
        with open("./data/res_csv/sft/sft-my.json", "r", encoding="utf-8") as f:
            training_data = json.load(f)
        results["训练数据"] = training_data
    
    return results

def check_lora_weights():
    """检查LoRA权重"""
    weights_info = {}
    
    weights_path = "./model_output/adapter_model.safetensors"
    if os.path.exists(weights_path):
        try:
            with safe_open(weights_path, framework="pt", device="cpu") as f:
                keys = list(f.keys())
                weights_info["参数数量"] = len(keys)
                weights_info["参数列表"] = keys[:10]  # 只显示前10个
                
                # 检查第一个权重的统计信息
                if keys:
                    first_tensor = f.get_tensor(keys[0])
                    weights_info["示例权重形状"] = str(first_tensor.shape)
                    weights_info["示例权重均值"] = float(first_tensor.float().mean())
                    weights_info["示例权重标准差"] = float(first_tensor.float().std())
                    
        except Exception as e:
            weights_info["错误"] = str(e)
    else:
        weights_info["状态"] = "权重文件不存在"
    
    return weights_info

def format_results(results):
    """格式化结果显示"""
    output = "# ChatGLM3 训练结果分析\n\n"
    
    # 训练结果
    if "训练结果" in results:
        train_results = results["训练结果"]
        output += "## 📊 训练结果\n"
        for key, value in train_results.items():
            if isinstance(value, float):
                output += f"- **{key}**: {value:.6f}\n"
            else:
                output += f"- **{key}**: {value}\n"
        output += "\n"
    
    # LoRA配置
    if "LoRA配置" in results:
        lora_config = results["LoRA配置"]
        output += "## ⚙️ LoRA配置\n"
        for key, value in lora_config.items():
            output += f"- **{key}**: {value}\n"
        output += "\n"
    
    # 训练数据
    if "训练数据" in results:
        training_data = results["训练数据"]
        output += f"## 📝 训练数据 ({len(training_data)} 个样本)\n"
        for i, sample in enumerate(training_data, 1):
            output += f"### 样本 {i}\n"
            output += f"**输入**: {sample['instruction']}\n\n"
            output += f"**输出**: {sample['output']}\n\n"
        output += "\n"
    
    return output

def format_weights_info(weights_info):
    """格式化权重信息"""
    output = "# LoRA权重分析\n\n"
    
    for key, value in weights_info.items():
        if key == "参数列表":
            output += f"## {key}\n"
            for param in value:
                output += f"- {param}\n"
            output += "\n"
        else:
            output += f"- **{key}**: {value}\n"
    
    return output

def create_demo():
    """创建演示界面"""
    
    with gr.Blocks(title="ChatGLM3 训练验证") as demo:
        gr.Markdown("# ChatGLM3 训练结果验证")
        gr.Markdown("由于内存限制，这里只显示训练结果分析，不加载完整模型")
        
        with gr.Tab("训练结果"):
            results_btn = gr.Button("分析训练结果", variant="primary")
            results_output = gr.Markdown()
            
            results_btn.click(
                fn=lambda: format_results(analyze_training_results()),
                outputs=results_output
            )
        
        with gr.Tab("LoRA权重"):
            weights_btn = gr.Button("检查LoRA权重", variant="primary")
            weights_output = gr.Markdown()
            
            weights_btn.click(
                fn=lambda: format_weights_info(check_lora_weights()),
                outputs=weights_output
            )
        
        with gr.Tab("模型状态"):
            status_output = gr.Markdown()
            
            def check_status():
                status = "## 模型文件状态\n\n"
                
                # 检查基础模型
                if os.path.exists("./chatglm3-6b"):
                    model_files = os.listdir("./chatglm3-6b")
                    status += f"✅ 基础模型存在 ({len(model_files)} 个文件)\n\n"
                else:
                    status += "❌ 基础模型不存在\n\n"
                
                # 检查训练输出
                if os.path.exists("./model_output"):
                    output_files = os.listdir("./model_output")
                    status += f"✅ 训练输出存在 ({len(output_files)} 个文件)\n\n"
                    status += "### 输出文件:\n"
                    for file in output_files:
                        status += f"- {file}\n"
                else:
                    status += "❌ 训练输出不存在\n\n"
                
                return status
            
            gr.Button("检查状态", variant="secondary").click(
                fn=check_status,
                outputs=status_output
            )
        
        # 自动加载初始状态
        demo.load(
            fn=lambda: (
                format_results(analyze_training_results()),
                format_weights_info(check_lora_weights())
            ),
            outputs=[results_output, weights_output]
        )
    
    return demo

def main():
    """主函数"""
    print("启动轻量级训练验证界面...")
    
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
