#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的模型测试脚本
绕过兼容性问题，直接测试模型推理
"""

import torch
from transformers import AutoTokenizer, AutoModel
import json

def test_model_simple():
    """简单测试模型"""
    print("=" * 50)
    print("简化模型测试")
    print("=" * 50)
    
    try:
        # 加载分词器和模型
        print("正在加载模型...")
        tokenizer = AutoTokenizer.from_pretrained("./chatglm3-6b", trust_remote_code=True)
        model = AutoModel.from_pretrained("./chatglm3-6b", trust_remote_code=True).eval()
        print("✅ 模型加载成功")
        
        # 测试基本对话功能
        test_queries = [
            "你好",
            "今天天气怎么样？",
            "是耶，挺好。",
            "哪天不hot。"
        ]
        
        for query in test_queries:
            print(f"\n🤖 测试输入: {query}")
            try:
                # 手动构建输入
                inputs = tokenizer.encode(query, return_tensors="pt")
                print(f"输入token数量: {inputs.shape[1]}")
                
                # 生成回复
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_length=inputs.shape[1] + 50,
                        num_return_sequences=1,
                        temperature=0.7,
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                # 解码回复
                response = tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
                print(f"✅ 模型回复: {response}")
                
            except Exception as e:
                print(f"❌ 生成失败: {e}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_training_effect():
    """测试训练效果"""
    print("\n" + "=" * 50)
    print("测试训练效果")
    print("=" * 50)
    
    try:
        # 读取训练数据
        with open('./data/res_csv/sft/sft-my.json', 'r', encoding='utf-8') as f:
            training_data = json.load(f)
        
        print(f"训练数据样本数量: {len(training_data)}")
        
        # 显示训练数据
        for i, sample in enumerate(training_data):
            print(f"\n样本 {i+1}:")
            print(f"  输入: {sample['instruction']}")
            print(f"  期望输出: {sample['output']}")
            
    except Exception as e:
        print(f"❌ 读取训练数据失败: {e}")

def check_model_files():
    """检查模型文件"""
    print("\n" + "=" * 50)
    print("检查模型文件")
    print("=" * 50)
    
    import os
    
    # 检查基础模型
    if os.path.exists("./chatglm3-6b"):
        print("✅ 基础模型目录存在")
        model_files = os.listdir("./chatglm3-6b")
        print(f"模型文件数量: {len(model_files)}")
    else:
        print("❌ 基础模型目录不存在")
    
    # 检查训练输出
    if os.path.exists("./model_output"):
        print("✅ 训练输出目录存在")
        output_files = os.listdir("./model_output")
        print(f"输出文件: {output_files}")
        
        # 检查LoRA权重
        if any("adapter" in f for f in output_files):
            print("✅ 找到LoRA适配器文件")
        else:
            print("⚠️  未找到LoRA适配器文件")
    else:
        print("❌ 训练输出目录不存在")

def main():
    """主函数"""
    print("ChatGLM3 简化测试脚本")
    
    # 检查文件
    check_model_files()
    
    # 测试训练效果
    test_training_effect()
    
    # 简单模型测试
    test_model_simple()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
